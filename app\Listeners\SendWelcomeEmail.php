<?php

namespace App\Listeners;

use App\Services\EmailService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendWelcomeEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        $user = $event->user;

        // Only send welcome emails to customers
        if ($user->role !== 'customer') {
            return;
        }

        try {
            EmailService::sendWelcomeEmail($user);
            Log::info("Welcome email sent to new customer: {$user->email}");
        } catch (\Exception $e) {
            Log::error("Failed to send welcome email to {$user->email}: " . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Registered $event, \Throwable $exception): void
    {
        Log::error("Welcome email listener failed for user {$event->user->email}: " . $exception->getMessage());
    }
}
