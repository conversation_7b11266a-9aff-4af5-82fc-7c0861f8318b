<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'origin_branch_id',
        'booking_id',
        'pickup_address',
        'pickup_latitude',
        'pickup_longitude',
        'pickup_place_id',
        'pickup_person_name',
        'pickup_person_phone',
        'delivery_address',
        'delivery_latitude',
        'delivery_longitude',
        'delivery_place_id',
        'receiver_name',
        'receiver_phone',
        'package_type',
        'package_weight',
        'package_description',
        'estimated_cost',
        'final_cost',
        'payment_method',
        'payment_status',
        'customer_payment_name',
        'customer_transaction_id',
        'payment_notes',
        'paid_at',
        'pickup_time_preference',
        'scheduled_pickup_time',
        'actual_pickup_time',
        'delivered_at',
        'status',
        'distance_km',
        'estimated_duration_minutes',
        'special_instructions',
        'cancellation_reason',
    ];

    protected $casts = [
        'pickup_latitude' => 'decimal:8',
        'pickup_longitude' => 'decimal:8',
        'delivery_latitude' => 'decimal:8',
        'delivery_longitude' => 'decimal:8',
        'package_weight' => 'decimal:2',
        'estimated_cost' => 'decimal:2',
        'final_cost' => 'decimal:2',
        'distance_km' => 'decimal:2',
        'scheduled_pickup_time' => 'datetime',
        'actual_pickup_time' => 'datetime',
        'delivered_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Valid status values for validation
     */
    public static $validStatuses = [
        'pending',
        'confirmed',

        'in_progress',
        'delivered',
        'cancelled'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (empty($booking->booking_id)) {
                $booking->booking_id = 'TTA' . strtoupper(Str::random(8));
            }
        });

        static::created(function ($booking) {
            // Fire booking created event for automation
            event(new \App\Events\BookingCreated($booking));
        });

        static::updated(function ($booking) {
            // Fire status change event if status changed
            if ($booking->isDirty('status')) {
                $originalStatus = $booking->getOriginal('status');
                $newStatus = $booking->status;

                event(new \App\Events\BookingStatusChanged(
                    $booking,
                    $originalStatus,
                    $newStatus
                ));
            }
        });
    }





    /**
     * Get the customer who made this booking
     */
    public function customer()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the origin branch for this booking
     */
    public function originBranch()
    {
        return $this->belongsTo(Branch::class, 'origin_branch_id');
    }



    /**
     * Get the payment for this booking
     */
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }

    /**
     * Get the review for this booking
     */
    public function review()
    {
        return $this->hasOne(Review::class);
    }

    /**
     * Check if booking can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    /**
     * Check if booking is active (in progress)
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['confirmed', 'in_progress']);
    }

    /**
     * Check if booking is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Get status badge color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'yellow',
            'confirmed' => 'blue',

            'in_progress' => 'purple',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get formatted status for display
     */
    public function getFormattedStatusAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Pending',
            'confirmed' => 'Confirmed',

            'in_progress' => 'In Progress',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            default => ucfirst(str_replace('_', ' ', $this->status))
        };
    }
}
