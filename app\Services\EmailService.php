<?php

namespace App\Services;

use App\Mail\AdminNewBookingEmail;
use App\Mail\BookingConfirmationEmail;
use App\Mail\BookingStatusUpdateEmail;
use App\Mail\PasswordResetEmail;
use App\Mail\WelcomeEmail;
use App\Models\Booking;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class EmailService
{
    /**
     * Send email to customer
     */
    public static function sendToCustomer(User $customer, string $subject, string $template, array $data = []): bool
    {
        try {
            // Check if email notifications are enabled
            if (!Setting::get('misc.enable_email_notifications', true)) {
                Log::info('Email notifications are disabled');
                return false;
            }

            $data = array_merge($data, [
                'customer' => $customer,
                'recipientEmail' => $customer->email,
                'showLogo' => true,
            ]);

            Mail::send($template, $data, function ($message) use ($customer, $subject) {
                $message->to($customer->email, $customer->name)
                       ->subject($subject)
                       ->from(
                           config('mail.from.address', '<EMAIL>'),
                           config('mail.from.name', 'TTAJet Courier Service')
                       );
            });

            Log::info("Email sent to customer {$customer->id} ({$customer->email}): {$subject}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send email to customer {$customer->id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email to admin(s)
     */
    public static function sendToAdmin(string $subject, string $template, array $data = []): bool
    {
        try {
            // Check if email notifications are enabled
            if (!Setting::get('misc.enable_email_notifications', true)) {
                Log::info('Email notifications are disabled');
                return false;
            }

            // Get admin email addresses from settings
            $adminEmails = self::getAdminEmails();
            
            if (empty($adminEmails)) {
                Log::warning('No admin email addresses configured');
                return false;
            }

            $data = array_merge($data, [
                'showLogo' => true,
            ]);

            foreach ($adminEmails as $adminEmail) {
                $data['recipientEmail'] = $adminEmail;
                
                Mail::send($template, $data, function ($message) use ($adminEmail, $subject) {
                    $message->to($adminEmail)
                           ->subject($subject)
                           ->from(
                               config('mail.from.address', '<EMAIL>'),
                               config('mail.from.name', 'TTAJet Courier Service')
                           );
                });
            }

            Log::info("Email sent to admins: {$subject}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send email to admins: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email to both customer and admin
     */
    public static function sendToCustomerAndAdmin(User $customer, string $customerSubject, string $adminSubject, string $customerTemplate, string $adminTemplate, array $customerData = [], array $adminData = []): bool
    {
        $customerSent = self::sendToCustomer($customer, $customerSubject, $customerTemplate, $customerData);
        $adminSent = self::sendToAdmin($adminSubject, $adminTemplate, $adminData);
        
        return $customerSent || $adminSent; // Return true if at least one email was sent
    }

    /**
     * Get admin email addresses from settings
     */
    public static function getAdminEmails(): array
    {
        $emails = [];
        
        // Get primary admin email from company settings
        $primaryEmail = Setting::get('company.email');
        if ($primaryEmail && filter_var($primaryEmail, FILTER_VALIDATE_EMAIL)) {
            $emails[] = $primaryEmail;
        }

        // Get additional admin emails from settings
        $additionalEmails = Setting::get('notifications.admin_emails', '');
        if ($additionalEmails) {
            $additionalEmailsArray = array_map('trim', explode(',', $additionalEmails));
            foreach ($additionalEmailsArray as $email) {
                if (filter_var($email, FILTER_VALIDATE_EMAIL) && !in_array($email, $emails)) {
                    $emails[] = $email;
                }
            }
        }

        // Fallback to admin users from database if no emails in settings
        if (empty($emails)) {
            $adminUsers = User::where('role', 'admin')->where('is_active', true)->get();
            foreach ($adminUsers as $admin) {
                if (!in_array($admin->email, $emails)) {
                    $emails[] = $admin->email;
                }
            }
        }

        return $emails;
    }

    /**
     * Format currency for emails
     */
    public static function formatCurrency(float $amount): string
    {
        $symbol = Setting::get('currency.symbol', '$');
        $position = Setting::get('currency.position', 'before');
        
        $formattedAmount = number_format($amount, 2);
        
        return $position === 'before' 
            ? $symbol . $formattedAmount 
            : $formattedAmount . $symbol;
    }

    /**
     * Get company information for emails
     */
    public static function getCompanyInfo(): array
    {
        return [
            'name' => Setting::get('company.name', 'TTAJet Courier Service'),
            'address' => Setting::get('company.address', 'Main Office Address'),
            'phone' => Setting::get('company.phone', '+233 XX XXX XXXX'),
            'email' => Setting::get('company.email', '<EMAIL>'),
        ];
    }

    /**
     * Generate tracking URL for emails
     */
    public static function getTrackingUrl(string $bookingId): string
    {
        return route('tracking') . '?booking_id=' . $bookingId;
    }

    /**
     * Generate booking details URL for emails
     */
    public static function getBookingUrl(int $bookingId): string
    {
        return route('booking.show', $bookingId);
    }

    /**
     * Test email configuration
     */
    public static function testEmailConfiguration(string $testEmail): bool
    {
        try {
            $data = [
                'title' => 'Email Configuration Test',
                'subtitle' => 'This is a test email to verify your email configuration',
                'recipientEmail' => $testEmail,
                'showLogo' => true,
            ];

            Mail::send('emails.test', $data, function ($message) use ($testEmail) {
                $message->to($testEmail)
                       ->subject('TTAJet Email Configuration Test')
                       ->from(
                           config('mail.from.address', '<EMAIL>'),
                           config('mail.from.name', 'TTAJet Courier Service')
                       );
            });

            Log::info("Test email sent to: {$testEmail}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send test email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send welcome email to new customer
     */
    public static function sendWelcomeEmail(User $customer): bool
    {
        if (!Setting::get('notifications.welcome_emails', true)) {
            return false;
        }

        try {
            Mail::to($customer->email)->send(new WelcomeEmail($customer));
            Log::info("Welcome email sent to customer: {$customer->email}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send welcome email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send booking confirmation email
     */
    public static function sendBookingConfirmation(Booking $booking): bool
    {
        if (!Setting::get('notifications.customer_notifications', true)) {
            return false;
        }

        try {
            $customer = $booking->customer;
            Mail::to($customer->email)->send(new BookingConfirmationEmail($booking, $customer));
            Log::info("Booking confirmation sent for booking: {$booking->booking_id}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send booking confirmation: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send booking status update email
     */
    public static function sendBookingStatusUpdate(
        Booking $booking,
        ?string $previousStatus = null,
        ?User $rider = null,
        ?string $cancellationReason = null,
        ?string $failureReason = null
    ): bool {
        if (!Setting::get('notifications.status_change_notifications', true)) {
            return false;
        }

        try {
            $customer = $booking->customer;
            Mail::to($customer->email)->send(
                new BookingStatusUpdateEmail(
                    $booking,
                    $customer,
                    $previousStatus,
                    $rider,
                    $cancellationReason,
                    $failureReason
                )
            );
            Log::info("Status update email sent for booking: {$booking->booking_id}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send status update email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send new booking notification to admins
     */
    public static function sendAdminNewBookingNotification(Booking $booking): bool
    {
        if (!Setting::get('notifications.booking_notifications', true)) {
            return false;
        }

        try {
            $adminEmails = self::getAdminEmails();
            if (empty($adminEmails)) {
                Log::warning("No admin emails configured for booking notifications");
                return false;
            }

            $customer = $booking->customer;
            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new AdminNewBookingEmail($booking, $customer));
            }

            Log::info("Admin notification sent for new booking: {$booking->booking_id}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send admin booking notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send password reset email
     */
    public static function sendPasswordResetEmail(User $user, string $resetUrl): bool
    {
        if (!Setting::get('notifications.password_reset_emails', true)) {
            return false;
        }

        try {
            Mail::to($user->email)->send(new PasswordResetEmail($user, $resetUrl));
            Log::info("Password reset email sent to: {$user->email}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send password reset email: " . $e->getMessage());
            return false;
        }
    }
}
