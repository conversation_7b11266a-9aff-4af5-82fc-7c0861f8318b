<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\CostCalculationService;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    protected $costCalculationService;

    public function __construct(CostCalculationService $costCalculationService)
    {
        $this->costCalculationService = $costCalculationService;
    }

    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = [
            'pricing' => Setting::getGroup('pricing'),
            'company' => Setting::getGroup('company'),
            'currency' => Setting::getGroup('currency'),
            'misc' => Setting::getGroup('misc'),
            'payments' => Setting::getGroup('payments'),
            'notifications' => Setting::getGroup('notifications'),
        ];

        // Get default values if settings don't exist yet
        $defaults = $this->getDefaultSettings();
        
        // Merge defaults with existing settings
        foreach ($defaults as $group => $groupDefaults) {
            if (!isset($settings[$group]) || empty($settings[$group])) {
                $settings[$group] = $groupDefaults;
            } else {
                $settings[$group] = array_merge($groupDefaults, $settings[$group]);
            }
        }

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            // Pricing settings
            'pricing.base_cost_document' => 'required|numeric|min:0',
            'pricing.base_cost_small' => 'required|numeric|min:0',
            'pricing.base_cost_medium' => 'required|numeric|min:0',
            'pricing.base_cost_large' => 'required|numeric|min:0',
            'pricing.cost_per_km' => 'required|numeric|min:0',
            'pricing.cost_per_kg' => 'required|numeric|min:0',
            'pricing.minimum_cost' => 'required|numeric|min:0',
            
            // Company settings
            'company.name' => 'required|string|max:255',
            'company.address' => 'required|string|max:500',
            'company.phone' => 'required|string|max:20',
            'company.email' => 'required|email|max:255',
            'company.latitude' => 'required|numeric|between:-90,90',
            'company.longitude' => 'required|numeric|between:-180,180',
            
            // Currency settings
            'currency.code' => 'required|string|size:3',
            'currency.symbol' => 'required|string|max:5',
            'currency.position' => 'required|in:before,after',
            
            // Misc settings
            'misc.timezone' => 'required|string|max:50',
            'misc.date_format' => 'required|string|max:20',
            'misc.time_format' => 'required|string|max:20',
            'misc.items_per_page' => 'required|integer|min:5|max:100',
            'misc.google_maps_api_key' => 'nullable|string|max:255',
            'misc.enable_notifications' => 'boolean',
            'misc.enable_email_notifications' => 'boolean',
            'misc.enable_sms_notifications' => 'boolean',

            // Notification settings
            'notifications.admin_emails' => 'nullable|string|max:500',
            'notifications.booking_notifications' => 'boolean',
            'notifications.customer_notifications' => 'boolean',
            'notifications.status_change_notifications' => 'boolean',
            'notifications.welcome_emails' => 'boolean',
            'notifications.password_reset_emails' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Update pricing settings
            if ($request->has('pricing')) {
                foreach ($request->pricing as $key => $value) {
                    Setting::set(
                        $key,
                        $value,
                        'number',
                        'pricing',
                        $this->getPricingLabel($key),
                        $this->getPricingDescription($key),
                        false
                    );
                }
            }

            // Update company settings
            if ($request->has('company')) {
                foreach ($request->company as $key => $value) {
                    $type = in_array($key, ['latitude', 'longitude']) ? 'number' : 'string';
                    Setting::set(
                        $key,
                        $value,
                        $type,
                        'company',
                        $this->getCompanyLabel($key),
                        $this->getCompanyDescription($key),
                        in_array($key, ['name', 'phone', 'email']) // Make some company info public
                    );
                }
            }

            // Update currency settings
            if ($request->has('currency')) {
                foreach ($request->currency as $key => $value) {
                    Setting::set(
                        $key,
                        $value,
                        'string',
                        'currency',
                        $this->getCurrencyLabel($key),
                        $this->getCurrencyDescription($key),
                        true // Currency settings are public
                    );
                }
            }

            // Update misc settings
            if ($request->has('misc')) {
                foreach ($request->misc as $key => $value) {
                    $type = in_array($key, ['items_per_page']) ? 'number' :
                           (in_array($key, ['enable_notifications', 'enable_email_notifications', 'enable_sms_notifications']) ? 'boolean' : 'string');

                    Setting::set(
                        $key,
                        $value,
                        $type,
                        'misc',
                        $this->getMiscLabel($key),
                        $this->getMiscDescription($key),
                        in_array($key, ['timezone', 'date_format', 'time_format']) // Some misc settings are public
                    );
                }
            }

            // Update notification settings
            if ($request->has('notifications')) {
                foreach ($request->notifications as $key => $value) {
                    $type = in_array($key, ['booking_notifications', 'customer_notifications', 'status_change_notifications', 'welcome_emails', 'password_reset_emails']) ? 'boolean' : 'string';

                    Setting::set(
                        $key,
                        $value,
                        $type,
                        'notifications',
                        $this->getNotificationLabel($key),
                        $this->getNotificationDescription($key),
                        false // Notification settings are private
                    );
                }
            }

            // Update payment settings
            if ($request->has('payments')) {
                foreach ($request->payments as $key => $value) {
                    Setting::set(
                        $key,
                        $value,
                        'string',
                        'payments',
                        $this->getPaymentLabel($key),
                        $this->getPaymentDescription($key),
                        true // Payment settings are public for frontend display
                    );
                }
            }

            // Clear settings cache
            Setting::clearCache();

            return back()->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update settings: ' . $e->getMessage());
        }
    }

    /**
     * Get default settings
     */
    protected function getDefaultSettings(): array
    {
        return [
            'pricing' => [
                'base_cost_document' => 15.00,
                'base_cost_small' => 20.00,
                'base_cost_medium' => 35.00,
                'base_cost_large' => 50.00,
                'cost_per_km' => 2.50,
                'cost_per_kg' => 2.50,
                'minimum_cost' => 15.00,
            ],
            'company' => [
                'name' => 'TTAJet Courier Service',
                'address' => 'Main Office Address',
                'phone' => '+**********',
                'email' => '<EMAIL>',
                'latitude' => 0.0,
                'longitude' => 0.0,
            ],
            'currency' => [
                'code' => 'USD',
                'symbol' => '$',
                'position' => 'before',
            ],
            'misc' => [
                'timezone' => 'UTC',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i',
                'items_per_page' => 20,
                'google_maps_api_key' => '',
                'enable_notifications' => true,
                'enable_email_notifications' => true,
                'enable_sms_notifications' => false,
            ],
            'payments' => [
                'offline_method_1_name' => 'Bank Transfer',
                'offline_method_1_instructions' => 'Transfer to our bank account and upload receipt',
                'offline_method_1_account' => 'Account: **********, Bank: ABC Bank',
                'offline_method_2_name' => 'Mobile Money',
                'offline_method_2_instructions' => 'Send money to our mobile money account',
                'offline_method_2_account' => 'MTN: **********',
                'offline_method_3_name' => '',
                'offline_method_3_instructions' => '',
                'offline_method_3_account' => '',
            ],
            'notifications' => [
                'admin_emails' => '',
                'booking_notifications' => true,
                'customer_notifications' => true,
                'status_change_notifications' => true,
                'welcome_emails' => true,
                'password_reset_emails' => true,
            ],
        ];
    }

    // Helper methods for labels and descriptions
    protected function getPricingLabel($key): string
    {
        $labels = [
            'base_cost_document' => 'Document Base Cost',
            'base_cost_small' => 'Small Package Base Cost',
            'base_cost_medium' => 'Medium Package Base Cost',
            'base_cost_large' => 'Large Package Base Cost',
            'cost_per_km' => 'Cost Per Kilometer',
            'cost_per_kg' => 'Cost Per Kilogram',
            'minimum_cost' => 'Minimum Delivery Cost',
        ];
        return $labels[$key] ?? ucwords(str_replace('_', ' ', $key));
    }

    protected function getPricingDescription($key): string
    {
        $descriptions = [
            'base_cost_document' => 'Base cost for document deliveries',
            'base_cost_small' => 'Base cost for small package deliveries',
            'base_cost_medium' => 'Base cost for medium package deliveries',
            'base_cost_large' => 'Base cost for large package deliveries',
            'cost_per_km' => 'Additional cost per kilometer of distance',
            'cost_per_kg' => 'Additional cost per kilogram of weight',
            'minimum_cost' => 'Minimum cost for any delivery',
        ];
        return $descriptions[$key] ?? '';
    }

    protected function getCompanyLabel($key): string
    {
        $labels = [
            'name' => 'Company Name',
            'address' => 'Company Address',
            'phone' => 'Company Phone',
            'email' => 'Company Email',
            'latitude' => 'Latitude',
            'longitude' => 'Longitude',
        ];
        return $labels[$key] ?? ucwords(str_replace('_', ' ', $key));
    }

    protected function getCompanyDescription($key): string
    {
        $descriptions = [
            'name' => 'Your company name as displayed to customers',
            'address' => 'Main office address for pickup origin',
            'phone' => 'Main contact phone number',
            'email' => 'Main contact email address',
            'latitude' => 'Latitude coordinate for company location',
            'longitude' => 'Longitude coordinate for company location',
        ];
        return $descriptions[$key] ?? '';
    }

    protected function getCurrencyLabel($key): string
    {
        $labels = [
            'code' => 'Currency Code',
            'symbol' => 'Currency Symbol',
            'position' => 'Symbol Position',
        ];
        return $labels[$key] ?? ucwords(str_replace('_', ' ', $key));
    }

    protected function getCurrencyDescription($key): string
    {
        $descriptions = [
            'code' => 'Three-letter currency code (e.g., USD, EUR)',
            'symbol' => 'Currency symbol to display (e.g., $, €)',
            'position' => 'Whether to show symbol before or after amount',
        ];
        return $descriptions[$key] ?? '';
    }

    protected function getMiscLabel($key): string
    {
        $labels = [
            'timezone' => 'System Timezone',
            'date_format' => 'Date Format',
            'time_format' => 'Time Format',
            'items_per_page' => 'Items Per Page',
            'google_maps_api_key' => 'Google Maps API Key',
            'enable_notifications' => 'Enable Notifications',
            'enable_email_notifications' => 'Enable Email Notifications',
            'enable_sms_notifications' => 'Enable SMS Notifications',
        ];
        return $labels[$key] ?? ucwords(str_replace('_', ' ', $key));
    }

    protected function getMiscDescription($key): string
    {
        $descriptions = [
            'timezone' => 'Default timezone for the application',
            'date_format' => 'Format for displaying dates',
            'time_format' => 'Format for displaying times',
            'items_per_page' => 'Number of items to show per page in listings',
            'google_maps_api_key' => 'API key for Google Maps integration',
            'enable_notifications' => 'Enable system notifications',
            'enable_email_notifications' => 'Enable email notifications to users',
            'enable_sms_notifications' => 'Enable SMS notifications to users',
        ];
        return $descriptions[$key] ?? '';
    }

    protected function getPaymentLabel($key): string
    {
        $labels = [
            'offline_method_1_name' => 'Offline Method 1 - Name',
            'offline_method_1_instructions' => 'Offline Method 1 - Instructions',
            'offline_method_1_account' => 'Offline Method 1 - Account Details',
            'offline_method_2_name' => 'Offline Method 2 - Name',
            'offline_method_2_instructions' => 'Offline Method 2 - Instructions',
            'offline_method_2_account' => 'Offline Method 2 - Account Details',
            'offline_method_3_name' => 'Offline Method 3 - Name',
            'offline_method_3_instructions' => 'Offline Method 3 - Instructions',
            'offline_method_3_account' => 'Offline Method 3 - Account Details',
        ];
        return $labels[$key] ?? ucwords(str_replace('_', ' ', $key));
    }

    protected function getPaymentDescription($key): string
    {
        $descriptions = [
            'offline_method_1_name' => 'Display name for the first offline payment method',
            'offline_method_1_instructions' => 'Instructions for customers using this payment method',
            'offline_method_1_account' => 'Account details (account number, bank name, etc.)',
            'offline_method_2_name' => 'Display name for the second offline payment method',
            'offline_method_2_instructions' => 'Instructions for customers using this payment method',
            'offline_method_2_account' => 'Account details (account number, bank name, etc.)',
            'offline_method_3_name' => 'Display name for the third offline payment method',
            'offline_method_3_instructions' => 'Instructions for customers using this payment method',
            'offline_method_3_account' => 'Account details (account number, bank name, etc.)',
        ];
        return $descriptions[$key] ?? '';
    }

    /**
     * Get notification setting labels
     */
    protected function getNotificationLabel(string $key): string
    {
        $labels = [
            'admin_emails' => 'Admin Email Addresses',
            'booking_notifications' => 'Booking Notifications',
            'customer_notifications' => 'Customer Notifications',
            'status_change_notifications' => 'Status Change Notifications',
            'welcome_emails' => 'Welcome Emails',
            'password_reset_emails' => 'Password Reset Emails',
        ];
        return $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
    }

    /**
     * Get notification setting descriptions
     */
    protected function getNotificationDescription(string $key): string
    {
        $descriptions = [
            'admin_emails' => 'Comma-separated list of email addresses to receive admin notifications',
            'booking_notifications' => 'Send email notifications to admins when new bookings are created',
            'customer_notifications' => 'Send email notifications to customers for various events',
            'status_change_notifications' => 'Send email notifications when booking status changes',
            'welcome_emails' => 'Send welcome emails to new customers',
            'password_reset_emails' => 'Send password reset emails to users',
        ];
        return $descriptions[$key] ?? '';
    }

    /**
     * Send test email
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $success = EmailService::testEmailConfiguration($request->email);

        return response()->json([
            'success' => $success,
            'message' => $success
                ? 'Test email sent successfully!'
                : 'Failed to send test email. Please check your email configuration.'
        ]);
    }
}
