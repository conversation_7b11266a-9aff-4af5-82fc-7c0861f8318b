<?php $__env->startSection('title', 'Booking Details - ' . $booking->booking_id); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Booking Details</h1>
                    <p class="text-gray-600 mt-1"><?php echo e($booking->booking_id); ?></p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="<?php echo e(route('booking.history')); ?>" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to History
                    </a>
                    <?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
                        <a href="<?php echo e(route('tracking')); ?>?booking_id=<?php echo e($booking->booking_id); ?>" 
                           class="brand-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                            <i class="fas fa-map-marker-alt mr-2"></i>Live Tracking
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Status Banner -->
            <div class="mb-8 p-6 rounded-xl
                <?php switch($booking->status):
                    case ('pending'): ?>
                        bg-yellow-50 border border-yellow-200
                        <?php break; ?>
                    <?php case ('confirmed'): ?>
                        bg-blue-50 border border-blue-200
                        <?php break; ?>
                    <?php case ('in_progress'): ?>
                        bg-purple-50 border border-purple-200
                        <?php break; ?>
                    <?php case ('completed'): ?>
                        bg-green-50 border border-green-200
                        <?php break; ?>
                    <?php case ('cancelled'): ?>
                        bg-red-50 border border-red-200
                        <?php break; ?>
                    <?php default: ?>
                        bg-gray-50 border border-gray-200
                <?php endswitch; ?>
            ">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full
                            <?php switch($booking->status):
                                case ('pending'): ?>
                                    bg-yellow-100
                                    <?php break; ?>
                                <?php case ('confirmed'): ?>
                                    bg-blue-100
                                    <?php break; ?>
                                <?php case ('in_progress'): ?>
                                    bg-purple-100
                                    <?php break; ?>
                                <?php case ('completed'): ?>
                                    bg-green-100
                                    <?php break; ?>
                                <?php case ('cancelled'): ?>
                                    bg-red-100
                                    <?php break; ?>
                                <?php default: ?>
                                    bg-gray-100
                            <?php endswitch; ?>
                        ">
                            <i class="fas 
                                <?php switch($booking->status):
                                    case ('pending'): ?>
                                        fa-clock text-yellow-600
                                        <?php break; ?>
                                    <?php case ('confirmed'): ?>
                                        fa-check text-blue-600
                                        <?php break; ?>
                                    <?php case ('in_progress'): ?>
                                        fa-truck text-purple-600
                                        <?php break; ?>
                                    <?php case ('completed'): ?>
                                        fa-check-circle text-green-600
                                        <?php break; ?>
                                    <?php case ('cancelled'): ?>
                                        fa-times-circle text-red-600
                                        <?php break; ?>
                                    <?php default: ?>
                                        fa-box text-gray-600
                                <?php endswitch; ?>
                                text-xl
                            "></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-bold
                                <?php switch($booking->status):
                                    case ('pending'): ?>
                                        text-yellow-900
                                        <?php break; ?>
                                    <?php case ('confirmed'): ?>
                                        text-blue-900
                                        <?php break; ?>
                                    <?php case ('in_progress'): ?>
                                        text-purple-900
                                        <?php break; ?>
                                    <?php case ('completed'): ?>
                                        text-green-900
                                        <?php break; ?>
                                    <?php case ('cancelled'): ?>
                                        text-red-900
                                        <?php break; ?>
                                    <?php default: ?>
                                        text-gray-900
                                <?php endswitch; ?>
                            ">
                                <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                            </h3>
                            <p class="text-sm
                                <?php switch($booking->status):
                                    case ('pending'): ?>
                                        text-yellow-700
                                        <?php break; ?>
                                    <?php case ('confirmed'): ?>
                                        text-blue-700
                                        <?php break; ?>
                                    <?php case ('in_progress'): ?>
                                        text-purple-700
                                        <?php break; ?>
                                    <?php case ('completed'): ?>
                                        text-green-700
                                        <?php break; ?>
                                    <?php case ('cancelled'): ?>
                                        text-red-700
                                        <?php break; ?>
                                    <?php default: ?>
                                        text-gray-700
                                <?php endswitch; ?>
                            ">
                                <?php switch($booking->status):
                                    case ('pending'): ?>
                                        Your booking is waiting for confirmation
                                        <?php break; ?>
                                    <?php case ('confirmed'): ?>
                                        Your booking has been confirmed and is being processed
                                        <?php break; ?>
                                    <?php case ('in_progress'): ?>
                                        Your package is on the way
                                        <?php break; ?>
                                    <?php case ('completed'): ?>
                                        Your package has been delivered successfully
                                        <?php break; ?>
                                    <?php case ('cancelled'): ?>
                                        This booking has been cancelled
                                        <?php break; ?>
                                    <?php default: ?>
                                        Booking status: <?php echo e($booking->status); ?>

                                <?php endswitch; ?>
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Opened At</p>
                        <p class="font-medium text-gray-900"><?php echo e($booking->created_at->format('M d, Y')); ?></p>
                        <p class="text-xs text-gray-500"><?php echo e($booking->created_at->format('h:i A')); ?></p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    

                    <!-- Live Tracking Map -->
                    <?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
                        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                            <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                                            <i class="fas fa-map-marker-alt text-orange-600 mr-2"></i>
                                            Live Tracking
                                        </h3>
                                        <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                            Real-time location and delivery progress
                                        </p>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <button onclick="refreshTracking()"
                                                class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                            <i class="fas fa-sync-alt mr-1" id="refresh-icon"></i>
                                            Refresh
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- Quick Status Info -->
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-3 h-3 rounded-full
                                                <?php switch($booking->status):
                                                    case ('confirmed'): ?>
                                                        bg-blue-400
                                                        <?php break; ?>

                                                    <?php case ('in_progress'): ?>
                                                        bg-purple-400 animate-pulse
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        bg-gray-400
                                                <?php endswitch; ?>
                                            "></div>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">
                                                <?php switch($booking->status):
                                                    case ('confirmed'): ?>
                                                        Preparing for Pickup
                                                        <?php break; ?>
                                                    <?php case ('in_progress'): ?>
                                                        Package Being Delivered
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                                                <?php endswitch; ?>
                                            </p>
                                        </div>
                                    </div>

                                    <?php if($booking->estimated_duration_minutes): ?>
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-clock text-gray-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">
                                                    ~<?php echo e($booking->estimated_duration_minutes); ?> minutes
                                                </p>
                                                <p class="text-xs text-gray-500">Estimated delivery time</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if($booking->distance_km): ?>
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-route text-gray-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">
                                                    <?php echo e(number_format($booking->distance_km, 1)); ?> km
                                                </p>
                                                <p class="text-xs text-gray-500">Total distance</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="p-0">
                                <?php if (isset($component)) { $__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.live-tracking-card','data' => ['booking' => $booking,'height' => '500px','showDetails' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('live-tracking-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['booking' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($booking),'height' => '500px','showDetails' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9)): ?>
<?php $attributes = $__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9; ?>
<?php unset($__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9)): ?>
<?php $component = $__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9; ?>
<?php unset($__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9); ?>
<?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    
                </div>
                
                <!-- Sidebar -->
                <div class="space-y-6">
                    
                    <!-- Booking Summary -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Booking Summary</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Booking ID</span>
                                <span class="text-sm font-medium text-gray-900"><?php echo e($booking->booking_id); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Distance</span>
                                <span class="text-sm font-medium text-gray-900"><?php echo e($booking->distance_km ?? 'N/A'); ?> km</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Estimated Duration</span>
                                <span class="text-sm font-medium text-gray-900" id="realtime-duration">Calculating...</span>
                                <script>
                                    // Calculate realtime duration using Google Maps Distance Matrix Service
                                    const origin = { lat: <?php echo e($booking->pickup_latitude); ?>, lng: <?php echo e($booking->pickup_longitude); ?> };
                                    const destination = { lat: <?php echo e($booking->delivery_latitude); ?>, lng: <?php echo e($booking->delivery_longitude); ?> };
                                    const service = new google.maps.DistanceMatrixService();
                                    
                                    service.getDistanceMatrix({
                                        origins: [origin],
                                        destinations: [destination],
                                        travelMode: google.maps.TravelMode.DRIVING,
                                    }, (response, status) => {
                                        if (status === 'OK') {
                                            const duration = response.rows[0].elements[0].duration.text;
                                            document.getElementById('realtime-duration').textContent = duration;
                                        }
                                    });
                                </script>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Payment Method</span>
                                <span class="text-sm font-medium text-gray-900"><?php echo e(ucfirst(str_replace('_', ' ', $booking->payment_method))); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Payment Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    <?php switch($booking->payment_status):
                                        case ('paid'): ?>
                                            bg-green-100 text-green-800
                                            <?php break; ?>
                                        <?php case ('pending'): ?>
                                            bg-yellow-100 text-yellow-800
                                            <?php break; ?>
                                        <?php case ('failed'): ?>
                                            bg-red-100 text-red-800
                                            <?php break; ?>
                                        <?php default: ?>
                                            bg-gray-100 text-gray-800
                                    <?php endswitch; ?>
                                ">
                                    <?php echo e(ucfirst($booking->payment_status)); ?>

                                </span>
                            </div>
                            <hr>
                            <div class="flex justify-between items-center">
                                <span class="text-base font-medium text-gray-900">Total Cost</span>
                                <span class="text-lg font-bold text-green-600"><?php echo e(\App\Models\Setting::formatCurrency($booking->final_cost ?? $booking->estimated_cost)); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Status -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Delivery Status</h3>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-truck text-blue-600 text-lg"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-900"><?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?></h4>
                                    <p class="text-sm text-gray-600">Booking #<?php echo e($booking->booking_id); ?></p>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Status</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <span class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1"></span>
                                        <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Created</span>
                                    <span class="text-sm text-gray-900"><?php echo e($booking->created_at->diffForHumans()); ?></span>
                                </div>
                                <?php if($booking->estimated_duration_minutes): ?>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Estimated Duration</span>
                                        <span class="text-sm text-gray-900"><?php echo e($booking->estimated_duration_minutes); ?> minutes</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6">
                            <div class="space-y-3">
                                <?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
                                    <a href="<?php echo e(route('tracking')); ?>?booking_id=<?php echo e($booking->booking_id); ?>" 
                                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white brand-orange hover:bg-orange-600">
                                        <i class="fas fa-map-marker-alt mr-2"></i>Live Tracking
                                    </a>
                                <?php endif; ?>
                                
                                <?php if($booking->status === 'completed' && !$booking->review): ?>
                                    <button onclick="openReviewModal()" 
                                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <i class="fas fa-star mr-2"></i>Leave Review
                                    </button>
                                <?php endif; ?>
                                
                                <?php if($booking->status === 'pending'): ?>
                                    <button onclick="cancelBooking()" 
                                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                                        <i class="fas fa-times mr-2"></i>Cancel Booking
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Map Styles -->
<style>
    /* Custom map styling */
    #booking-map {
        border-radius: 0;
        transition: height 0.3s ease-in-out;
    }

    /* Custom info window styling */
    .gm-style .gm-style-iw-c {
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .gm-style .gm-style-iw-t::after {
        background: white;
    }

    /* Map control buttons */
    .map-control-btn {
        transition: all 0.2s ease;
    }

    .map-control-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Loading animation */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .map-loading {
        animation: pulse 2s infinite;
    }

    /* Route info overlay */
    .route-info-overlay {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Custom scrollbar for info windows */
    .gm-style .gm-style-iw-c::-webkit-scrollbar {
        width: 4px;
    }

    .gm-style .gm-style-iw-c::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .gm-style .gm-style-iw-c::-webkit-scrollbar-thumb {
        background: #F97316;
        border-radius: 2px;
    }

    /* Fullscreen mode */
    .fullscreen-map {
        transition: all 0.3s ease-in-out;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .route-info-overlay {
            bottom: 8px;
            left: 8px;
            right: 8px;
        }

        .route-info-overlay .grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .map-controls {
            top: 8px;
            left: 8px;
        }

        .map-control-btn {
            padding: 8px 12px;
            font-size: 11px;
        }

        #booking-map {
            min-height: 250px;
        }
    }

    /* Enhanced marker animations */
    @keyframes markerBounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .marker-bounce {
        animation: markerBounce 2s infinite;
    }

    /* Status indicator */
    .status-indicator {
        position: relative;
    }

    .status-indicator::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -12px;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: currentColor;
    }

    .status-confirmed::before {
        background: #3B82F6;
        animation: pulse 2s infinite;
    }

    .status-in-progress::before {
        background: #8B5CF6;
        animation: pulse 1s infinite;
    }

    .status-completed::before {
        background: #10B981;
    }
</style>

<!-- Google Maps API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(config('services.google_maps.api_key')); ?>&libraries=places&callback=initGoogleMaps"></script>

<script>
// Map variables
let map;
let directionsService;
let directionsRenderer;
let pickupMarker;
let deliveryMarker;

// Initialize Google Maps when API is loaded
function initGoogleMaps() {
    console.log('Google Maps API loaded');
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeTrackingMap);
    } else {
        initializeTrackingMap();
    }
}

function initializeTrackingMap() {
    if (typeof google === 'undefined' || !google.maps) {
        console.warn('Google Maps API not loaded yet');
        return;
    }

    // Initialize map
    const mapElement = document.getElementById('booking-map');
    if (mapElement) {
        map = new google.maps.Map(mapElement, {
            zoom: 12,
            center: { lat: 5.6037, lng: -0.1870 }, // Accra, Ghana
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                }
            ]
        });

        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({
            suppressMarkers: true,
            polylineOptions: {
                strokeColor: '#F59E0B',
                strokeWeight: 4
            }
        });
        directionsRenderer.setMap(map);

        // Load booking route
        updateMapAndRoute();
    }

    // Map toggle functionality
    document.getElementById('toggle-map').addEventListener('click', function() {
        const mapContainer = document.getElementById('booking-map');
        const button = this;

        if (mapContainer.style.height === '200px') {
            mapContainer.style.height = '400px';
            button.innerHTML = '<i class="fas fa-compress-arrows-alt mr-1"></i>Collapse';
        } else {
            mapContainer.style.height = '200px';
            button.innerHTML = '<i class="fas fa-expand-arrows-alt mr-1"></i>Expand';
        }

        // Trigger map resize
        setTimeout(() => {
            if (map) {
                google.maps.event.trigger(map, 'resize');
                if (pickupMarker && deliveryMarker) {
                    const bounds = new google.maps.LatLngBounds();
                    bounds.extend(pickupMarker.getPosition());
                    bounds.extend(deliveryMarker.getPosition());
                    map.fitBounds(bounds);
                }
            }
        }, 300);
    });
}

function updateMapAndRoute() {
    const pickupLat = <?php echo e($booking->pickup_latitude ?? 5.6037); ?>;
    const pickupLng = <?php echo e($booking->pickup_longitude ?? -0.1870); ?>;
    const deliveryLat = <?php echo e($booking->delivery_latitude ?? 5.6037); ?>;
    const deliveryLng = <?php echo e($booking->delivery_longitude ?? -0.1870); ?>;

    if (!map || isNaN(pickupLat) || isNaN(pickupLng) || isNaN(deliveryLat) || isNaN(deliveryLng)) {
        return;
    }

    // Clear existing markers
    if (pickupMarker) pickupMarker.setMap(null);
    if (deliveryMarker) deliveryMarker.setMap(null);

    // Add pickup marker
    pickupMarker = new google.maps.Marker({
        position: { lat: pickupLat, lng: pickupLng },
        map: map,
        title: 'Pickup Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                    <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Add delivery marker
    deliveryMarker = new google.maps.Marker({
        position: { lat: deliveryLat, lng: deliveryLng },
        map: map,
        title: 'Delivery Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                    <path d="M12 8v4l3 3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Add info windows
    const pickupInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Pickup Location</h4>
                <p class="text-sm"><?php echo e($booking->pickup_address); ?></p>
                <p class="text-xs text-gray-600"><?php echo e($booking->sender_name); ?></p>
            </div>
        `
    });

    const deliveryInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Delivery Location</h4>
                <p class="text-sm"><?php echo e($booking->delivery_address); ?></p>
                <p class="text-xs text-gray-600"><?php echo e($booking->receiver_name); ?></p>
            </div>
        `
    });

    pickupMarker.addListener('click', () => {
        pickupInfoWindow.open(map, pickupMarker);
    });

    deliveryMarker.addListener('click', () => {
        deliveryInfoWindow.open(map, deliveryMarker);
    });

    // Calculate and display route
    directionsService.route({
        origin: { lat: pickupLat, lng: pickupLng },
        destination: { lat: deliveryLat, lng: deliveryLng },
        travelMode: google.maps.TravelMode.DRIVING
    }, function(result, status) {
        if (status === 'OK') {
            directionsRenderer.setDirections(result);

            // Update distance and duration if not already set
            const route = result.routes[0];
            if (route && route.legs && route.legs.length > 0) {
                const leg = route.legs[0];
                const distanceElement = document.getElementById('estimated-distance');
                const durationElement = document.getElementById('estimated-duration');

                if (distanceElement.textContent === '-- km') {
                    distanceElement.textContent = leg.distance.text;
                }
                if (durationElement.textContent === '-- mins') {
                    durationElement.textContent = leg.duration.text;
                }
            }

            // Fit map to show route
            const bounds = new google.maps.LatLngBounds();
            bounds.extend({ lat: pickupLat, lng: pickupLng });
            bounds.extend({ lat: deliveryLat, lng: deliveryLng });
            map.fitBounds(bounds);
        }
    });
}

// Auto-refresh for active bookings
<?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
    let autoRefreshInterval;
    let refreshCount = 0;
    const maxRefreshes = 120; // Stop auto-refresh after 2 hours (120 * 60 seconds)

    function startAutoRefresh() {
        autoRefreshInterval = setInterval(() => {
            refreshCount++;
            if (refreshCount >= maxRefreshes) {
                stopAutoRefresh();
                return;
            }

            // Refresh the live tracking map if it exists
            if (typeof refreshTrackingMap<?php echo e($booking->id); ?> === 'function') {
                refreshTrackingMap<?php echo e($booking->id); ?>();
            }

            // Optionally refresh the entire page every 10 minutes for status updates
            if (refreshCount % 10 === 0) {
                checkForStatusUpdate();
            }
        }, 60000); // Refresh every 60 seconds
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    function checkForStatusUpdate() {
        // Check if booking status has changed
        fetch(`/api/booking/<?php echo e($booking->id); ?>/status`)
            .then(response => response.json())
            .then(data => {
                if (data.status !== '<?php echo e($booking->status); ?>') {
                    // Status changed, reload the page
                    window.location.reload();
                }
            })
            .catch(error => {
                console.log('Status check failed:', error);
            });
    }

    // Start auto-refresh when page loads
    document.addEventListener('DOMContentLoaded', function() {
        startAutoRefresh();
    });

    // Stop auto-refresh when page is hidden/user switches tabs
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
        }
    });

    // Stop auto-refresh when user leaves the page
    window.addEventListener('beforeunload', function() {
        stopAutoRefresh();
    });
<?php endif; ?>

function refreshTracking() {
    const refreshIcon = document.getElementById('refresh-icon');

    // Add spinning animation
    refreshIcon.classList.add('fa-spin');

    // Refresh the live tracking map
    if (typeof refreshTrackingMap<?php echo e($booking->id); ?> === 'function') {
        refreshTrackingMap<?php echo e($booking->id); ?>();
    }

    // Check for status updates
    <?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
        checkForStatusUpdate();
    <?php endif; ?>

    // Remove spinning animation after 2 seconds
    setTimeout(() => {
        refreshIcon.classList.remove('fa-spin');
    }, 2000);
}

// Show last updated time
function updateLastRefreshTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();

    // Create or update last refresh indicator
    let refreshIndicator = document.getElementById('last-refresh');
    if (!refreshIndicator) {
        refreshIndicator = document.createElement('div');
        refreshIndicator.id = 'last-refresh';
        refreshIndicator.className = 'text-xs text-gray-500 mt-2';

        const trackingCard = document.querySelector('.live-tracking-card');
        if (trackingCard) {
            trackingCard.appendChild(refreshIndicator);
        }
    }

    refreshIndicator.textContent = `Last updated: ${timeString}`;
}

// Update last refresh time on page load
document.addEventListener('DOMContentLoaded', function() {
    updateLastRefreshTime();
});

function openReviewModal() {
    // Placeholder for review modal
    alert('Review functionality will be implemented soon!');
}

function cancelBooking() {
    if (confirm('Are you sure you want to cancel this booking?')) {
        // Implement cancellation logic
        alert('Cancellation functionality will be implemented soon!');
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/booking/show.blade.php ENDPATH**/ ?>